// معدل ضريبة القيمة المضافة في المملكة العربية السعودية
const VAT_RATE = 0.15;

// مصفوفة لحفظ عناصر الفاتورة
let invoiceItems = [];

// العناصر من DOM
const productForm = document.getElementById('productForm');
const invoiceItemsContainer = document.getElementById('invoiceItems');
const invoiceSummary = document.getElementById('invoiceSummary');
const subtotalElement = document.getElementById('subtotal');
const vatAmountElement = document.getElementById('vatAmount');
const totalElement = document.getElementById('total');
const clearInvoiceBtn = document.getElementById('clearInvoice');
const printInvoiceBtn = document.getElementById('printInvoice');

// إضافة مستمع للنموذج
productForm.addEventListener('submit', function(e) {
    e.preventDefault();
    addProductToInvoice();
});

// إضافة مستمعين للأزرار
clearInvoiceBtn.addEventListener('click', clearInvoice);
printInvoiceBtn.addEventListener('click', printInvoice);

// دالة إضافة منتج إلى الفاتورة
function addProductToInvoice() {
    const productName = document.getElementById('productName').value.trim();
    const productPrice = parseFloat(document.getElementById('productPrice').value);
    const quantity = parseInt(document.getElementById('quantity').value);

    if (!productName || productPrice <= 0 || quantity <= 0) {
        alert('يرجى إدخال بيانات صحيحة للمنتج');
        return;
    }

    // حساب المجموع الفرعي للمنتج
    const subtotal = productPrice * quantity;
    const vatAmount = subtotal * VAT_RATE;
    const total = subtotal + vatAmount;

    // إنشاء عنصر فاتورة جديد
    const invoiceItem = {
        id: Date.now(), // معرف فريد
        name: productName,
        price: productPrice,
        quantity: quantity,
        subtotal: subtotal,
        vatAmount: vatAmount,
        total: total
    };

    // إضافة العنصر إلى المصفوفة
    invoiceItems.push(invoiceItem);

    // تحديث العرض
    updateInvoiceDisplay();

    // مسح النموذج
    productForm.reset();
    document.getElementById('quantity').value = 1;
}

// دالة تحديث عرض الفاتورة
function updateInvoiceDisplay() {
    if (invoiceItems.length === 0) {
        invoiceItemsContainer.innerHTML = '<p class="empty-message">لا توجد منتجات في الفاتورة</p>';
        invoiceSummary.style.display = 'none';
        return;
    }

    // عرض عناصر الفاتورة
    let itemsHTML = '';
    invoiceItems.forEach(item => {
        itemsHTML += `
            <div class="invoice-item">
                <div class="item-details">
                    <div class="item-name">${item.name}</div>
                    <div class="item-info">
                        ${item.price.toFixed(2)} ريال × ${item.quantity} = ${item.subtotal.toFixed(2)} ريال
                        <br>
                        ضريبة القيمة المضافة: ${item.vatAmount.toFixed(2)} ريال
                    </div>
                </div>
                <div class="item-total">${item.total.toFixed(2)} ريال</div>
                <button class="remove-item" onclick="removeItem(${item.id})">حذف</button>
            </div>
        `;
    });

    invoiceItemsContainer.innerHTML = itemsHTML;

    // حساب المجاميع
    const totalSubtotal = invoiceItems.reduce((sum, item) => sum + item.subtotal, 0);
    const totalVAT = invoiceItems.reduce((sum, item) => sum + item.vatAmount, 0);
    const grandTotal = invoiceItems.reduce((sum, item) => sum + item.total, 0);

    // تحديث عرض المجاميع
    subtotalElement.textContent = `${totalSubtotal.toFixed(2)} ريال`;
    vatAmountElement.textContent = `${totalVAT.toFixed(2)} ريال`;
    totalElement.textContent = `${grandTotal.toFixed(2)} ريال`;

    // إظهار ملخص الفاتورة
    invoiceSummary.style.display = 'block';
}

// دالة حذف عنصر من الفاتورة
function removeItem(itemId) {
    invoiceItems = invoiceItems.filter(item => item.id !== itemId);
    updateInvoiceDisplay();
}

// دالة مسح الفاتورة
function clearInvoice() {
    if (invoiceItems.length === 0) {
        return;
    }

    if (confirm('هل أنت متأكد من مسح جميع عناصر الفاتورة؟')) {
        invoiceItems = [];
        updateInvoiceDisplay();
    }
}

// دالة طباعة الفاتورة
function printInvoice() {
    if (invoiceItems.length === 0) {
        alert('لا توجد عناصر للطباعة');
        return;
    }

    // إنشاء نافذة طباعة جديدة
    const printWindow = window.open('', '_blank');
    const printContent = generatePrintContent();
    
    printWindow.document.write(printContent);
    printWindow.document.close();
    printWindow.print();
}

// دالة إنشاء محتوى الطباعة
function generatePrintContent() {
    const currentDate = new Date().toLocaleDateString('ar-SA');
    const currentTime = new Date().toLocaleTimeString('ar-SA');
    
    let itemsHTML = '';
    invoiceItems.forEach((item, index) => {
        itemsHTML += `
            <tr>
                <td>${index + 1}</td>
                <td>${item.name}</td>
                <td>${item.quantity}</td>
                <td>${item.price.toFixed(2)}</td>
                <td>${item.subtotal.toFixed(2)}</td>
                <td>${item.vatAmount.toFixed(2)}</td>
                <td>${item.total.toFixed(2)}</td>
            </tr>
        `;
    });

    const totalSubtotal = invoiceItems.reduce((sum, item) => sum + item.subtotal, 0);
    const totalVAT = invoiceItems.reduce((sum, item) => sum + item.vatAmount, 0);
    const grandTotal = invoiceItems.reduce((sum, item) => sum + item.total, 0);

    return `
        <!DOCTYPE html>
        <html lang="ar" dir="rtl">
        <head>
            <meta charset="UTF-8">
            <title>فاتورة مبيعات</title>
            <style>
                body { font-family: Arial, sans-serif; margin: 20px; }
                .header { text-align: center; margin-bottom: 30px; }
                .invoice-info { margin-bottom: 20px; }
                table { width: 100%; border-collapse: collapse; margin-bottom: 20px; }
                th, td { border: 1px solid #ddd; padding: 8px; text-align: center; }
                th { background-color: #f2f2f2; }
                .totals { margin-top: 20px; }
                .total-row { display: flex; justify-content: space-between; margin: 5px 0; }
                .grand-total { font-weight: bold; font-size: 1.2em; border-top: 2px solid #000; padding-top: 10px; }
            </style>
        </head>
        <body>
            <div class="header">
                <h1>فاتورة مبيعات</h1>
                <p>المملكة العربية السعودية</p>
            </div>
            
            <div class="invoice-info">
                <p><strong>التاريخ:</strong> ${currentDate}</p>
                <p><strong>الوقت:</strong> ${currentTime}</p>
            </div>
            
            <table>
                <thead>
                    <tr>
                        <th>م</th>
                        <th>اسم المنتج</th>
                        <th>الكمية</th>
                        <th>السعر الوحدة</th>
                        <th>المجموع الفرعي</th>
                        <th>ضريبة القيمة المضافة</th>
                        <th>الإجمالي</th>
                    </tr>
                </thead>
                <tbody>
                    ${itemsHTML}
                </tbody>
            </table>
            
            <div class="totals">
                <div class="total-row">
                    <span>المجموع الفرعي:</span>
                    <span>${totalSubtotal.toFixed(2)} ريال</span>
                </div>
                <div class="total-row">
                    <span>ضريبة القيمة المضافة (15%):</span>
                    <span>${totalVAT.toFixed(2)} ريال</span>
                </div>
                <div class="total-row grand-total">
                    <span>المجموع الإجمالي:</span>
                    <span>${grandTotal.toFixed(2)} ريال</span>
                </div>
            </div>
        </body>
        </html>
    `;
}

// تحديث العرض عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    updateInvoiceDisplay();
});
