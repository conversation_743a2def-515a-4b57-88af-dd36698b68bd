// معدل ضريبة القيمة المضافة في المملكة العربية السعودية
const VAT_RATE = 0.15;

// مصفوفة لحفظ عناصر الفاتورة
let invoiceItems = [];

// العناصر من DOM
const productForm = document.getElementById('productForm');
const invoiceItemsContainer = document.getElementById('invoiceItems');
const invoiceSummary = document.getElementById('invoiceSummary');
const subtotalElement = document.getElementById('subtotal');
const vatAmountElement = document.getElementById('vatAmount');
const totalElement = document.getElementById('total');
const clearInvoiceBtn = document.getElementById('clearInvoice');
const printInvoiceBtn = document.getElementById('printInvoice');

// إضافة مستمع للنموذج
productForm.addEventListener('submit', function(e) {
    e.preventDefault();
    addProductToInvoice();
});

// إضافة مستمعين للأزرار
clearInvoiceBtn.addEventListener('click', clearInvoice);
printInvoiceBtn.addEventListener('click', printInvoice);

// دالة إضافة منتج إلى الفاتورة
function addProductToInvoice() {
    const productName = document.getElementById('productName').value.trim();
    const productPrice = parseFloat(document.getElementById('productPrice').value);
    const quantity = parseInt(document.getElementById('quantity').value);

    if (!productName || productPrice <= 0 || quantity <= 0) {
        alert('يرجى إدخال بيانات صحيحة للمنتج');
        return;
    }

    // حساب المجموع الفرعي للمنتج
    const subtotal = productPrice * quantity;
    const vatAmount = subtotal * VAT_RATE;
    const total = subtotal + vatAmount;

    // إنشاء عنصر فاتورة جديد
    const invoiceItem = {
        id: Date.now(), // معرف فريد
        name: productName,
        price: productPrice,
        quantity: quantity,
        subtotal: subtotal,
        vatAmount: vatAmount,
        total: total
    };

    // إضافة العنصر إلى المصفوفة
    invoiceItems.push(invoiceItem);

    // تحديث العرض
    updateInvoiceDisplay();

    // مسح النموذج
    productForm.reset();
    document.getElementById('quantity').value = 1;
}

// دالة تحديث عرض الفاتورة
function updateInvoiceDisplay() {
    if (invoiceItems.length === 0) {
        invoiceItemsContainer.innerHTML = '<p class="empty-message">لا توجد منتجات في الفاتورة</p>';
        invoiceSummary.style.display = 'none';
        clearQRCode();
        return;
    }

    // عرض عناصر الفاتورة
    let itemsHTML = '';
    invoiceItems.forEach(item => {
        itemsHTML += `
            <div class="invoice-item">
                <div class="item-details">
                    <div class="item-name">${item.name}</div>
                    <div class="item-info">
                        ${item.price.toFixed(2)} ريال × ${item.quantity} = ${item.subtotal.toFixed(2)} ريال
                        <br>
                        ضريبة القيمة المضافة: ${item.vatAmount.toFixed(2)} ريال
                    </div>
                </div>
                <div class="item-total">${item.total.toFixed(2)} ريال</div>
                <button class="remove-item" onclick="removeItem(${item.id})">حذف</button>
            </div>
        `;
    });

    invoiceItemsContainer.innerHTML = itemsHTML;

    // حساب المجاميع
    const totalSubtotal = invoiceItems.reduce((sum, item) => sum + item.subtotal, 0);
    const totalVAT = invoiceItems.reduce((sum, item) => sum + item.vatAmount, 0);
    const grandTotal = invoiceItems.reduce((sum, item) => sum + item.total, 0);

    // تحديث عرض المجاميع
    subtotalElement.textContent = `${totalSubtotal.toFixed(2)} ريال`;
    vatAmountElement.textContent = `${totalVAT.toFixed(2)} ريال`;
    totalElement.textContent = `${grandTotal.toFixed(2)} ريال`;

    // إظهار ملخص الفاتورة
    invoiceSummary.style.display = 'block';

    // إنشاء QR Code
    generateQRCode();
}

// دالة حذف عنصر من الفاتورة
function removeItem(itemId) {
    invoiceItems = invoiceItems.filter(item => item.id !== itemId);
    updateInvoiceDisplay();
}

// دالة مسح الفاتورة
function clearInvoice() {
    if (invoiceItems.length === 0) {
        return;
    }

    if (confirm('هل أنت متأكد من مسح جميع عناصر الفاتورة؟')) {
        invoiceItems = [];
        updateInvoiceDisplay();
    }
}

// دالة طباعة الفاتورة
function printInvoice() {
    if (invoiceItems.length === 0) {
        alert('لا توجد عناصر للطباعة');
        return;
    }

    // إنشاء نافذة طباعة جديدة
    const printWindow = window.open('', '_blank');

    // إنشاء QR Code للطباعة
    generatePrintQRCode(printWindow);
}

// دالة إنشاء محتوى الطباعة
function generatePrintContent(qrDataURL = '') {
    const currentDate = new Date().toLocaleDateString('ar-SA');
    const currentTime = new Date().toLocaleTimeString('ar-SA');
    
    let itemsHTML = '';
    invoiceItems.forEach((item, index) => {
        itemsHTML += `
            <tr>
                <td>${index + 1}</td>
                <td>${item.name}</td>
                <td>${item.quantity}</td>
                <td>${item.price.toFixed(2)}</td>
                <td>${item.subtotal.toFixed(2)}</td>
                <td>${item.vatAmount.toFixed(2)}</td>
                <td>${item.total.toFixed(2)}</td>
            </tr>
        `;
    });

    const totalSubtotal = invoiceItems.reduce((sum, item) => sum + item.subtotal, 0);
    const totalVAT = invoiceItems.reduce((sum, item) => sum + item.vatAmount, 0);
    const grandTotal = invoiceItems.reduce((sum, item) => sum + item.total, 0);

    return `
        <!DOCTYPE html>
        <html lang="ar" dir="rtl">
        <head>
            <meta charset="UTF-8">
            <title>فاتورة مبيعات</title>
            <style>
                body { font-family: Arial, sans-serif; margin: 20px; }
                .header { text-align: center; margin-bottom: 30px; }
                .invoice-info { margin-bottom: 20px; }
                table { width: 100%; border-collapse: collapse; margin-bottom: 20px; }
                th, td { border: 1px solid #ddd; padding: 8px; text-align: center; }
                th { background-color: #f2f2f2; }
                .totals { margin-top: 20px; }
                .total-row { display: flex; justify-content: space-between; margin: 5px 0; }
                .grand-total { font-weight: bold; font-size: 1.2em; border-top: 2px solid #000; padding-top: 10px; }
            </style>
        </head>
        <body>
            <div class="header">
                <h1>فاتورة مبيعات</h1>
                <p>المملكة العربية السعودية</p>
            </div>
            
            <div class="invoice-info">
                <p><strong>التاريخ:</strong> ${currentDate}</p>
                <p><strong>الوقت:</strong> ${currentTime}</p>
            </div>
            
            <table>
                <thead>
                    <tr>
                        <th>م</th>
                        <th>اسم المنتج</th>
                        <th>الكمية</th>
                        <th>السعر الوحدة</th>
                        <th>المجموع الفرعي</th>
                        <th>ضريبة القيمة المضافة</th>
                        <th>الإجمالي</th>
                    </tr>
                </thead>
                <tbody>
                    ${itemsHTML}
                </tbody>
            </table>
            
            <div class="totals">
                <div class="total-row">
                    <span>المجموع الفرعي:</span>
                    <span>${totalSubtotal.toFixed(2)} ريال</span>
                </div>
                <div class="total-row">
                    <span>ضريبة القيمة المضافة (15%):</span>
                    <span>${totalVAT.toFixed(2)} ريال</span>
                </div>
                <div class="total-row grand-total">
                    <span>المجموع الإجمالي:</span>
                    <span>${grandTotal.toFixed(2)} ريال</span>
                </div>
            </div>

            ${qrDataURL ? `
            <div style="text-align: center; margin-top: 30px; page-break-inside: avoid;">
                <h3>رمز QR للفاتورة</h3>
                <img src="${qrDataURL}" alt="QR Code" style="border: 2px solid #000; border-radius: 8px; margin: 10px 0;">
                <p style="font-size: 0.9rem; color: #666;">امسح الرمز للحصول على تفاصيل الفاتورة</p>
            </div>
            ` : ''}
        </body>
        </html>
    `;
}

// دالة إنشاء QR Code
function generateQRCode() {
    const qrContainer = document.getElementById('qrcode');

    // مسح QR Code السابق
    qrContainer.innerHTML = '';

    // إنشاء بيانات الفاتورة للـ QR Code
    const invoiceData = createInvoiceData();

    // إنشاء QR Code
    QRCode.toCanvas(qrContainer, invoiceData, {
        width: 150,
        height: 150,
        color: {
            dark: '#2c5530',
            light: '#ffffff'
        },
        margin: 2
    }, function (error) {
        if (error) {
            console.error('خطأ في إنشاء QR Code:', error);
            qrContainer.innerHTML = '<p style="color: red;">خطأ في إنشاء رمز QR</p>';
        }
    });
}

// دالة إنشاء بيانات الفاتورة للـ QR Code
function createInvoiceData() {
    const currentDate = new Date().toLocaleDateString('ar-SA');
    const currentTime = new Date().toLocaleTimeString('ar-SA');

    const totalSubtotal = invoiceItems.reduce((sum, item) => sum + item.subtotal, 0);
    const totalVAT = invoiceItems.reduce((sum, item) => sum + item.vatAmount, 0);
    const grandTotal = invoiceItems.reduce((sum, item) => sum + item.total, 0);

    // إنشاء نص مفصل للفاتورة
    let itemsList = '';
    invoiceItems.forEach((item, index) => {
        itemsList += `${index + 1}. ${item.name} - الكمية: ${item.quantity} - السعر: ${item.price.toFixed(2)} ريال - الإجمالي: ${item.total.toFixed(2)} ريال\n`;
    });

    const invoiceText = `
فاتورة مبيعات - المملكة العربية السعودية
التاريخ: ${currentDate}
الوقت: ${currentTime}

المنتجات:
${itemsList}
المجموع الفرعي: ${totalSubtotal.toFixed(2)} ريال
ضريبة القيمة المضافة (15%): ${totalVAT.toFixed(2)} ريال
المجموع الإجمالي: ${grandTotal.toFixed(2)} ريال

شكراً لتعاملكم معنا
    `.trim();

    return invoiceText;
}

// دالة إنشاء QR Code للطباعة
function generatePrintQRCode(printWindow) {
    const invoiceData = createInvoiceData();

    // إنشاء canvas مؤقت للـ QR Code
    const tempCanvas = document.createElement('canvas');

    QRCode.toCanvas(tempCanvas, invoiceData, {
        width: 150,
        height: 150,
        color: {
            dark: '#000000',
            light: '#ffffff'
        },
        margin: 2
    }, function (error) {
        if (error) {
            console.error('خطأ في إنشاء QR Code للطباعة:', error);
            // طباعة بدون QR Code في حالة الخطأ
            const printContent = generatePrintContent('');
            printWindow.document.write(printContent);
            printWindow.document.close();
            printWindow.print();
            return;
        }

        // تحويل Canvas إلى Data URL
        const qrDataURL = tempCanvas.toDataURL('image/png');

        // إنشاء محتوى الطباعة مع QR Code
        const printContent = generatePrintContent(qrDataURL);

        printWindow.document.write(printContent);
        printWindow.document.close();
        printWindow.print();
    });
}

// دالة مسح QR Code
function clearQRCode() {
    const qrContainer = document.getElementById('qrcode');
    qrContainer.innerHTML = '';
}

// تحديث العرض عند تحميل الصفحة
document.addEventListener('DOMContentLoaded', function() {
    updateInvoiceDisplay();
});
