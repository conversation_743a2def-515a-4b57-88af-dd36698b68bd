// معدل ضريبة القيمة المضافة في المملكة العربية السعودية
const VAT_RATE = 0.15;

// مصفوفات لحفظ البيانات
let invoiceItems = [];
let products = [];
let salesHistory = [];
let vatEnabled = true;
let currentInvoiceNumber = null;

// إعدادات الشركة
let companySettings = {
    name: 'شركة المثال التجارية',
    vatNumber: '123456789012345',
    address: 'الرياض، المملكة العربية السعودية',
    phone: '+966501234567'
};

// العناصر من DOM
const invoiceItemsContainer = document.getElementById('invoiceItems');
const invoiceSummary = document.getElementById('invoiceSummary');
const subtotalElement = document.getElementById('subtotal');
const vatAmountElement = document.getElementById('vatAmount');
const totalElement = document.getElementById('total');
const clearInvoiceBtn = document.getElementById('clearInvoice');
const printInvoiceBtn = document.getElementById('printInvoice');
const printSimpleBtn = document.getElementById('printSimple');
const completeInvoiceBtn = document.getElementById('completeInvoice');
const vatToggle = document.getElementById('vatToggle');
const vatRow = document.getElementById('vatRow');
const productsGrid = document.getElementById('productsGrid');
const addProductForm = document.getElementById('addProductForm');
const productsList = document.getElementById('productsList');
const invoiceNumberElement = document.getElementById('invoiceNumber');
const salesList = document.getElementById('salesList');
const todaySalesElement = document.getElementById('todaySales');
const todayInvoicesElement = document.getElementById('todayInvoices');
const averageInvoiceElement = document.getElementById('averageInvoice');
const companyForm = document.getElementById('companyForm');
const companyNameInput = document.getElementById('companyName');
const vatNumberInput = document.getElementById('vatNumber');
const companyAddressInput = document.getElementById('companyAddress');
const companyPhoneInput = document.getElementById('companyPhone');

// إضافة مستمعين للأحداث
document.addEventListener('DOMContentLoaded', function() {
    initializeApp();
});

// وظائف التنقل بين التبويبات
function initializeApp() {
    // تحميل البيانات المحفوظة
    loadProducts();
    loadInvoiceData();
    loadSalesHistory();
    loadCompanySettings();

    // إعداد التبويبات
    setupTabs();

    // إعداد مستمعي الأحداث
    setupEventListeners();

    // إنشاء رقم فاتورة جديد
    generateNewInvoiceNumber();

    // تحديث العرض
    updateProductsGrid();
    updateInvoiceDisplay();
    updateSalesDisplay();
}

function setupTabs() {
    const navTabs = document.querySelectorAll('.nav-tab');
    const tabContents = document.querySelectorAll('.tab-content');

    navTabs.forEach(tab => {
        tab.addEventListener('click', function() {
            const targetTab = this.dataset.tab;

            // إزالة الفئة النشطة من جميع التبويبات
            navTabs.forEach(t => t.classList.remove('active'));
            tabContents.forEach(content => content.classList.remove('active'));

            // إضافة الفئة النشطة للتبويب المحدد
            this.classList.add('active');
            document.getElementById(targetTab + '-section').classList.add('active');
        });
    });
}

function setupEventListeners() {
    // مستمعي أحداث الفاتورة
    clearInvoiceBtn.addEventListener('click', clearInvoice);
    completeInvoiceBtn.addEventListener('click', completeInvoice);
    printInvoiceBtn.addEventListener('click', printInvoice);
    if (printSimpleBtn) {
        printSimpleBtn.addEventListener('click', printSimpleInvoice);
    }

    // مستمع تبديل الضريبة
    vatToggle.addEventListener('change', function() {
        vatEnabled = this.checked;
        vatRow.style.display = vatEnabled ? 'flex' : 'none';
        updateInvoiceDisplay();
        saveInvoiceData();
    });

    // مستمع إضافة منتج جديد
    addProductForm.addEventListener('submit', function(e) {
        e.preventDefault();
        addNewProduct();
    });

    // مستمعي فلاتر المبيعات
    const filterBtns = document.querySelectorAll('.filter-btn');
    filterBtns.forEach(btn => {
        btn.addEventListener('click', function() {
            filterBtns.forEach(b => b.classList.remove('active'));
            this.classList.add('active');
            updateSalesDisplay(this.dataset.filter);
        });
    });

    // مستمع نموذج إعدادات الشركة
    if (companyForm) {
        companyForm.addEventListener('submit', function(e) {
            e.preventDefault();
            saveCompanySettings();
        });
    }
}

// وظائف إدارة الفواتير
function generateNewInvoiceNumber() {
    const today = new Date();
    const dateStr = today.toISOString().slice(0, 10).replace(/-/g, '');
    const timeStr = today.getTime().toString().slice(-4);
    currentInvoiceNumber = `INV-${dateStr}-${timeStr}`;

    if (invoiceNumberElement) {
        invoiceNumberElement.textContent = currentInvoiceNumber;
    }
}

function completeInvoice() {
    if (invoiceItems.length === 0) {
        alert('لا توجد منتجات في الفاتورة');
        return;
    }

    // حساب المجاميع
    const totalSubtotal = invoiceItems.reduce((sum, item) => sum + item.subtotal, 0);
    const totalVAT = invoiceItems.reduce((sum, item) => sum + item.vatAmount, 0);
    const grandTotal = invoiceItems.reduce((sum, item) => sum + item.total, 0);

    // إنشاء سجل المبيعة
    const sale = {
        id: Date.now(),
        invoiceNumber: currentInvoiceNumber,
        date: new Date().toISOString(),
        items: [...invoiceItems],
        subtotal: totalSubtotal,
        vat: totalVAT,
        total: grandTotal,
        vatEnabled: vatEnabled
    };

    // إضافة إلى سجل المبيعات
    salesHistory.push(sale);
    saveSalesHistory();

    // مسح الفاتورة الحالية
    invoiceItems = [];
    generateNewInvoiceNumber();
    updateInvoiceDisplay();
    updateSalesDisplay();
    saveInvoiceData();

    alert(`تم حفظ الفاتورة رقم ${sale.invoiceNumber} بنجاح!`);
}

// وظائف إدارة المنتجات
function addNewProduct() {
    const name = document.getElementById('newProductName').value.trim();
    const price = parseFloat(document.getElementById('newProductPrice').value);
    const category = document.getElementById('newProductCategory').value.trim();

    if (!name || price <= 0) {
        alert('يرجى إدخال اسم المنتج وسعر صحيح');
        return;
    }

    const newProduct = {
        id: Date.now(),
        name: name,
        price: price,
        category: category || 'عام'
    };

    products.push(newProduct);
    saveProducts();
    updateProductsGrid();
    updateProductsList();

    // مسح النموذج
    addProductForm.reset();
    alert('تم إضافة المنتج بنجاح');
}

function deleteProduct(productId) {
    if (confirm('هل أنت متأكد من حذف هذا المنتج؟')) {
        products = products.filter(product => product.id !== productId);
        saveProducts();
        updateProductsGrid();
        updateProductsList();
    }
}

function editProduct(productId) {
    const product = products.find(p => p.id === productId);
    if (!product) return;

    const newName = prompt('اسم المنتج الجديد:', product.name);
    if (newName === null) return;

    const newPrice = prompt('السعر الجديد:', product.price);
    if (newPrice === null) return;

    const newCategory = prompt('الفئة الجديدة:', product.category);
    if (newCategory === null) return;

    if (newName.trim() && parseFloat(newPrice) > 0) {
        product.name = newName.trim();
        product.price = parseFloat(newPrice);
        product.category = newCategory.trim() || 'عام';

        saveProducts();
        updateProductsGrid();
        updateProductsList();
        alert('تم تحديث المنتج بنجاح');
    } else {
        alert('يرجى إدخال بيانات صحيحة');
    }
}

// دالة إضافة منتج إلى الفاتورة
function addProductToInvoice(productId, quantity = 1) {
    const product = products.find(p => p.id === productId);
    if (!product) return;

    // التحقق من وجود المنتج في الفاتورة
    const existingItem = invoiceItems.find(item => item.productId === productId);

    if (existingItem) {
        existingItem.quantity += quantity;
    } else {
        const invoiceItem = {
            id: Date.now(),
            productId: productId,
            name: product.name,
            price: product.price,
            quantity: quantity
        };
        invoiceItems.push(invoiceItem);
    }

    updateInvoiceDisplay();
    saveInvoiceData();
}

// دالة تحديث عرض الفاتورة
function updateInvoiceDisplay() {
    if (invoiceItems.length === 0) {
        invoiceItemsContainer.innerHTML = '<p class="empty-message">لا توجد منتجات في الفاتورة</p>';
        invoiceSummary.style.display = 'none';
        clearQRCode();
        return;
    }

    // حساب المجاميع لكل عنصر
    invoiceItems.forEach(item => {
        item.subtotal = item.price * item.quantity;
        item.vatAmount = vatEnabled ? item.subtotal * VAT_RATE : 0;
        item.total = item.subtotal + item.vatAmount;
    });

    // عرض عناصر الفاتورة
    let itemsHTML = '';
    invoiceItems.forEach(item => {
        itemsHTML += `
            <div class="invoice-item">
                <div class="item-details">
                    <div class="item-name">${item.name}</div>
                    <div class="item-info">
                        ${item.price.toFixed(2)} ريال × ${item.quantity} = ${item.subtotal.toFixed(2)} ريال
                        ${vatEnabled ? `<br>ضريبة القيمة المضافة: ${item.vatAmount.toFixed(2)} ريال` : ''}
                    </div>
                </div>
                <div class="item-total">${item.total.toFixed(2)} ريال</div>
                <div class="item-controls">
                    <button class="qty-btn" onclick="changeQuantity(${item.id}, -1)">-</button>
                    <span class="qty-display">${item.quantity}</span>
                    <button class="qty-btn" onclick="changeQuantity(${item.id}, 1)">+</button>
                    <button class="remove-item" onclick="removeItem(${item.id})">حذف</button>
                </div>
            </div>
        `;
    });

    invoiceItemsContainer.innerHTML = itemsHTML;

    // حساب المجاميع الإجمالية
    const totalSubtotal = invoiceItems.reduce((sum, item) => sum + item.subtotal, 0);
    const totalVAT = invoiceItems.reduce((sum, item) => sum + item.vatAmount, 0);
    const grandTotal = invoiceItems.reduce((sum, item) => sum + item.total, 0);

    // تحديث عرض المجاميع
    subtotalElement.textContent = `${totalSubtotal.toFixed(2)} ريال`;
    vatAmountElement.textContent = `${totalVAT.toFixed(2)} ريال`;
    totalElement.textContent = `${grandTotal.toFixed(2)} ريال`;

    // إظهار/إخفاء صف الضريبة
    vatRow.style.display = vatEnabled ? 'flex' : 'none';

    // إظهار ملخص الفاتورة
    invoiceSummary.style.display = 'block';

    // إنشاء QR Code
    generateQRCode();
}

// دالة تغيير الكمية
function changeQuantity(itemId, change) {
    const item = invoiceItems.find(item => item.id === itemId);
    if (!item) return;

    item.quantity += change;
    if (item.quantity <= 0) {
        removeItem(itemId);
    } else {
        updateInvoiceDisplay();
        saveInvoiceData();
    }
}

// دالة حذف عنصر من الفاتورة
function removeItem(itemId) {
    invoiceItems = invoiceItems.filter(item => item.id !== itemId);
    updateInvoiceDisplay();
    saveInvoiceData();
}

// دالة مسح الفاتورة
function clearInvoice() {
    if (invoiceItems.length === 0) {
        return;
    }

    if (confirm('هل أنت متأكد من مسح جميع عناصر الفاتورة؟')) {
        invoiceItems = [];
        updateInvoiceDisplay();
        saveInvoiceData();
    }
}

// وظائف عرض المنتجات
function updateProductsGrid() {
    if (products.length === 0) {
        productsGrid.innerHTML = '<p class="empty-message">لا توجد منتجات. اذهب إلى إدارة المنتجات لإضافة منتجات جديدة.</p>';
        return;
    }

    let gridHTML = '';
    products.forEach(product => {
        gridHTML += `
            <div class="product-card" onclick="addProductToInvoice(${product.id})">
                <div class="product-name">${product.name}</div>
                <div class="product-price">${product.price.toFixed(2)} ريال</div>
                <div class="product-category">${product.category}</div>
            </div>
        `;
    });

    productsGrid.innerHTML = gridHTML;
}

function updateProductsList() {
    if (products.length === 0) {
        productsList.innerHTML = '<p class="empty-message">لا توجد منتجات محفوظة</p>';
        return;
    }

    let listHTML = `
        <div class="product-row header">
            <div>اسم المنتج</div>
            <div>السعر</div>
            <div>الفئة</div>
            <div>الإجراءات</div>
        </div>
    `;

    products.forEach(product => {
        listHTML += `
            <div class="product-row">
                <div>${product.name}</div>
                <div>${product.price.toFixed(2)} ريال</div>
                <div>${product.category}</div>
                <div class="product-actions">
                    <button class="btn-edit" onclick="editProduct(${product.id})">تعديل</button>
                    <button class="btn-delete" onclick="deleteProduct(${product.id})">حذف</button>
                </div>
            </div>
        `;
    });

    productsList.innerHTML = listHTML;
}

// وظائف عرض المبيعات
function updateSalesDisplay(filter = 'today') {
    updateSalesSummary(filter);
    updateSalesList(filter);
}

function updateSalesSummary(filter = 'today') {
    const filteredSales = filterSales(filter);

    const totalSales = filteredSales.reduce((sum, sale) => sum + sale.total, 0);
    const invoiceCount = filteredSales.length;
    const averageInvoice = invoiceCount > 0 ? totalSales / invoiceCount : 0;

    if (todaySalesElement) todaySalesElement.textContent = `${totalSales.toFixed(2)} ريال`;
    if (todayInvoicesElement) todayInvoicesElement.textContent = invoiceCount;
    if (averageInvoiceElement) averageInvoiceElement.textContent = `${averageInvoice.toFixed(2)} ريال`;
}

function updateSalesList(filter = 'today') {
    const filteredSales = filterSales(filter);

    if (filteredSales.length === 0) {
        salesList.innerHTML = '<p class="empty-message">لا توجد مبيعات مسجلة</p>';
        return;
    }

    let listHTML = `
        <div class="sale-row header">
            <div>رقم الفاتورة</div>
            <div>التاريخ</div>
            <div>المجموع الفرعي</div>
            <div>الضريبة</div>
            <div>الإجمالي</div>
        </div>
    `;

    filteredSales.reverse().forEach(sale => {
        const date = new Date(sale.date);
        const dateStr = date.toLocaleDateString('ar-SA');
        const timeStr = date.toLocaleTimeString('ar-SA', { hour: '2-digit', minute: '2-digit' });

        listHTML += `
            <div class="sale-row">
                <div>${sale.invoiceNumber}</div>
                <div>${dateStr} ${timeStr}</div>
                <div>${sale.subtotal.toFixed(2)} ريال</div>
                <div>${sale.vatEnabled ? sale.vat.toFixed(2) + ' ريال' : 'معفى'}</div>
                <div>${sale.total.toFixed(2)} ريال</div>
            </div>
        `;
    });

    salesList.innerHTML = listHTML;
}

function filterSales(filter) {
    const now = new Date();
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());

    switch (filter) {
        case 'today':
            return salesHistory.filter(sale => {
                const saleDate = new Date(sale.date);
                return saleDate >= today;
            });
        case 'week':
            const weekAgo = new Date(today);
            weekAgo.setDate(weekAgo.getDate() - 7);
            return salesHistory.filter(sale => {
                const saleDate = new Date(sale.date);
                return saleDate >= weekAgo;
            });
        case 'month':
            const monthAgo = new Date(today);
            monthAgo.setMonth(monthAgo.getMonth() - 1);
            return salesHistory.filter(sale => {
                const saleDate = new Date(sale.date);
                return saleDate >= monthAgo;
            });
        case 'all':
        default:
            return salesHistory;
    }
}

// دالة طباعة الفاتورة
function printInvoice() {
    if (invoiceItems.length === 0) {
        alert('لا توجد عناصر للطباعة');
        return;
    }

    // إنشاء نافذة طباعة جديدة
    const printWindow = window.open('', '_blank');

    if (!printWindow) {
        alert('تم حظر النافذة المنبثقة. يرجى السماح بالنوافذ المنبثقة وإعادة المحاولة.');
        return;
    }

    // إنشاء QR Code للطباعة أو طباعة مباشرة
    try {
        generatePrintQRCode(printWindow);
    } catch (error) {
        console.error('خطأ في إنشاء QR Code، سيتم الطباعة بدونه:', error);
        // طباعة مباشرة بدون QR Code
        const printContent = generatePrintContent('');
        printWindow.document.write(printContent);
        printWindow.document.close();
        setTimeout(() => {
            printWindow.print();
        }, 500);
    }
}

// دالة طباعة مبسطة بدون QR Code
function printSimpleInvoice() {
    if (invoiceItems.length === 0) {
        alert('لا توجد عناصر للطباعة');
        return;
    }

    const printWindow = window.open('', '_blank');

    if (!printWindow) {
        alert('تم حظر النافذة المنبثقة. يرجى السماح بالنوافذ المنبثقة وإعادة المحاولة.');
        return;
    }

    // طباعة مباشرة بدون QR Code
    const printContent = generatePrintContent('');
    printWindow.document.write(printContent);
    printWindow.document.close();

    setTimeout(() => {
        printWindow.print();
    }, 500);
}

// دالة إنشاء محتوى الطباعة
function generatePrintContent(qrDataURL = '') {
    const currentDate = new Date().toLocaleDateString('ar-SA');
    const currentTime = new Date().toLocaleTimeString('ar-SA');
    
    let itemsHTML = '';
    invoiceItems.forEach((item, index) => {
        itemsHTML += `
            <tr>
                <td>${index + 1}</td>
                <td>${item.name}</td>
                <td>${item.quantity}</td>
                <td>${item.price.toFixed(2)} ريال</td>
                <td>${item.subtotal.toFixed(2)} ريال</td>
                ${vatEnabled ? `<td>${item.vatAmount.toFixed(2)} ريال</td>` : ''}
                <td>${item.total.toFixed(2)} ريال</td>
            </tr>
        `;
    });

    const totalSubtotal = invoiceItems.reduce((sum, item) => sum + item.subtotal, 0);
    const totalVAT = invoiceItems.reduce((sum, item) => sum + item.vatAmount, 0);
    const grandTotal = invoiceItems.reduce((sum, item) => sum + item.total, 0);

    return `
        <!DOCTYPE html>
        <html lang="ar" dir="rtl">
        <head>
            <meta charset="UTF-8">
            <title>فاتورة مبيعات</title>
            <style>
                body { font-family: Arial, sans-serif; margin: 20px; }
                .header { text-align: center; margin-bottom: 30px; border-bottom: 2px solid #000; padding-bottom: 20px; }
                .company-info { margin-bottom: 20px; }
                .invoice-info { margin-bottom: 20px; display: flex; justify-content: space-between; }
                .invoice-details { text-align: right; }
                table { width: 100%; border-collapse: collapse; margin-bottom: 20px; }
                th, td { border: 1px solid #ddd; padding: 8px; text-align: center; }
                th { background-color: #f2f2f2; }
                .totals { margin-top: 20px; }
                .total-row { display: flex; justify-content: space-between; margin: 5px 0; }
                .grand-total { font-weight: bold; font-size: 1.2em; border-top: 2px solid #000; padding-top: 10px; }
                .qr-section { text-align: center; margin-top: 30px; page-break-inside: avoid; }
            </style>
        </head>
        <body>
            <div class="header">
                <h1>${companySettings.name}</h1>
                <p><strong>الرقم الضريبي:</strong> ${companySettings.vatNumber}</p>
                <p>${companySettings.address}</p>
                ${companySettings.phone ? `<p><strong>الهاتف:</strong> ${companySettings.phone}</p>` : ''}
            </div>

            <div class="invoice-info">
                <div class="company-info">
                    <h2>فاتورة ضريبية</h2>
                    <p><strong>رقم الفاتورة:</strong> ${currentInvoiceNumber || 'غير محدد'}</p>
                </div>
                <div class="invoice-details">
                    <p><strong>التاريخ:</strong> ${currentDate}</p>
                    <p><strong>الوقت:</strong> ${currentTime}</p>
                </div>
            </div>
            
            <table>
                <thead>
                    <tr>
                        <th>م</th>
                        <th>اسم المنتج</th>
                        <th>الكمية</th>
                        <th>السعر الوحدة (بدون ضريبة)</th>
                        <th>المجموع الفرعي</th>
                        ${vatEnabled ? '<th>ضريبة القيمة المضافة (15%)</th>' : ''}
                        <th>الإجمالي</th>
                    </tr>
                </thead>
                <tbody>
                    ${itemsHTML}
                </tbody>
            </table>
            
            <div class="totals">
                <div class="total-row">
                    <span>المجموع الفرعي:</span>
                    <span>${totalSubtotal.toFixed(2)} ريال</span>
                </div>
                ${vatEnabled ? `
                <div class="total-row">
                    <span>ضريبة القيمة المضافة (15%):</span>
                    <span>${totalVAT.toFixed(2)} ريال</span>
                </div>
                ` : `
                <div class="total-row">
                    <span>بدون ضريبة قيمة مضافة</span>
                    <span>--</span>
                </div>
                `}
                <div class="total-row grand-total">
                    <span>المجموع الإجمالي:</span>
                    <span>${grandTotal.toFixed(2)} ريال</span>
                </div>
            </div>

            ${qrDataURL ? `
            <div class="qr-section">
                <h3>رمز الاستجابة السريعة (QR Code)</h3>
                <p style="font-size: 0.9rem; margin-bottom: 10px;">مطابق لمعايير هيئة الزكاة والدخل والجمارك</p>
                <img src="${qrDataURL}" alt="QR Code" style="border: 2px solid #000; border-radius: 8px; margin: 10px 0; width: 150px; height: 150px;">
                <p style="font-size: 0.8rem; color: #666;">امسح الرمز للتحقق من صحة الفاتورة</p>
            </div>
            ` : ''}

            <div style="margin-top: 40px; padding-top: 20px; border-top: 1px solid #ddd; font-size: 0.9rem; color: #666;">
                <p><strong>ملاحظات مهمة:</strong></p>
                <ul style="text-align: right; margin-right: 20px;">
                    <li>هذه فاتورة ضريبية صادرة وفقاً لأنظمة هيئة الزكاة والدخل والجمارك</li>
                    <li>يمكن التحقق من صحة الفاتورة عبر مسح رمز QR أعلاه</li>
                    ${vatEnabled ? '<li>تم احتساب ضريبة القيمة المضافة بنسبة 15%</li>' : '<li>هذه الفاتورة معفاة من ضريبة القيمة المضافة</li>'}
                </ul>
                <p style="text-align: center; margin-top: 20px;"><strong>شكراً لتعاملكم معنا</strong></p>
            </div>
        </body>
        </html>
    `;
}

// دالة إنشاء QR Code
function generateQRCode() {
    const qrContainer = document.getElementById('qrcode');

    if (!qrContainer) return;

    // مسح QR Code السابق
    qrContainer.innerHTML = '';

    try {
        // إنشاء بيانات الفاتورة للـ QR Code
        const invoiceData = createInvoiceData();

        // إنشاء QR Code
        QRCode.toCanvas(qrContainer, invoiceData, {
            width: 150,
            height: 150,
            color: {
                dark: '#2c5530',
                light: '#ffffff'
            },
            margin: 2,
            errorCorrectionLevel: 'M'
        }, function (error) {
            if (error) {
                console.error('خطأ في إنشاء QR Code:', error);
                qrContainer.innerHTML = '<p style="color: red;">خطأ في إنشاء رمز QR</p>';
            }
        });
    } catch (error) {
        console.error('خطأ عام في إنشاء QR Code:', error);
        qrContainer.innerHTML = '<p style="color: red;">خطأ في إنشاء رمز QR</p>';
    }
}

// دالة إنشاء بيانات الفاتورة للـ QR Code مطابق لمعايير هيئة الزكاة والدخل
function createInvoiceData() {
    const currentDate = new Date();

    const totalSubtotal = invoiceItems.reduce((sum, item) => sum + item.subtotal, 0);
    const totalVAT = invoiceItems.reduce((sum, item) => sum + item.vatAmount, 0);
    const grandTotal = invoiceItems.reduce((sum, item) => sum + item.total, 0);

    // إنشاء QR Code بتنسيق TLV حسب معايير هيئة الزكاة والدخل
    return createZATCAQRCode(companySettings.name, companySettings.vatNumber, currentDate, grandTotal, totalVAT);
}

// دالة إنشاء QR Code بتنسيق مبسط لهيئة الزكاة والدخل
function createZATCAQRCode(sellerName, vatNumber, invoiceDate, totalAmount, vatAmount) {
    // تحويل التاريخ إلى تنسيق ISO
    const isoDate = invoiceDate.toISOString();

    // إنشاء بيانات مبسطة للاختبار
    const qrData = [
        `اسم البائع: ${sellerName}`,
        `الرقم الضريبي: ${vatNumber}`,
        `تاريخ الفاتورة: ${isoDate}`,
        `إجمالي الفاتورة: ${totalAmount.toFixed(2)} ريال`,
        `ضريبة القيمة المضافة: ${vatAmount.toFixed(2)} ريال`,
        `رقم الفاتورة: ${currentInvoiceNumber || 'غير محدد'}`
    ].join('\n');

    return qrData;
}

// دالة إنشاء TLV (Tag-Length-Value) مبسطة
function createTLV(tag, value) {
    // تحويل القيم إلى نص بسيط للاختبار
    return `${tag}:${value.length}:${value}|`;
}

// دالة إنشاء QR Code للطباعة
function generatePrintQRCode(printWindow) {
    try {
        const invoiceData = createInvoiceData();
        console.log('QR Data:', invoiceData); // للتشخيص

        // إنشاء canvas مؤقت للـ QR Code
        const tempCanvas = document.createElement('canvas');

        QRCode.toCanvas(tempCanvas, invoiceData, {
            width: 150,
            height: 150,
            color: {
                dark: '#000000',
                light: '#ffffff'
            },
            margin: 2,
            errorCorrectionLevel: 'M'
        }, function (error) {
            if (error) {
                console.error('خطأ في إنشاء QR Code للطباعة:', error);
                // طباعة بدون QR Code في حالة الخطأ
                const printContent = generatePrintContent('');
                printWindow.document.write(printContent);
                printWindow.document.close();
                printWindow.print();
                return;
            }

            try {
                // تحويل Canvas إلى Data URL
                const qrDataURL = tempCanvas.toDataURL('image/png');

                // إنشاء محتوى الطباعة مع QR Code
                const printContent = generatePrintContent(qrDataURL);

                printWindow.document.write(printContent);
                printWindow.document.close();

                // تأخير قصير قبل الطباعة للتأكد من تحميل المحتوى
                setTimeout(() => {
                    printWindow.print();
                }, 500);

            } catch (printError) {
                console.error('خطأ في الطباعة:', printError);
                alert('حدث خطأ أثناء إعداد الطباعة');
                printWindow.close();
            }
        });
    } catch (error) {
        console.error('خطأ عام في الطباعة:', error);
        alert('حدث خطأ أثناء إعداد الطباعة');
        if (printWindow) {
            printWindow.close();
        }
    }
}

// دالة مسح QR Code
function clearQRCode() {
    const qrContainer = document.getElementById('qrcode');
    qrContainer.innerHTML = '';
}

// وظائف حفظ وتحميل البيانات
function saveProducts() {
    localStorage.setItem('pos_products', JSON.stringify(products));
}

function loadProducts() {
    const savedProducts = localStorage.getItem('pos_products');
    if (savedProducts) {
        products = JSON.parse(savedProducts);
    } else {
        // إضافة بعض المنتجات الافتراضية
        products = [
            { id: 1, name: 'قهوة عربية', price: 15.00, category: 'مشروبات' },
            { id: 2, name: 'شاي أحمر', price: 8.00, category: 'مشروبات' },
            { id: 3, name: 'كعك', price: 12.00, category: 'حلويات' },
            { id: 4, name: 'بسكويت', price: 6.00, category: 'حلويات' }
        ];
        saveProducts();
    }
}

function saveInvoiceData() {
    const invoiceData = {
        items: invoiceItems,
        vatEnabled: vatEnabled
    };
    localStorage.setItem('pos_invoice', JSON.stringify(invoiceData));
}

function loadInvoiceData() {
    const savedInvoice = localStorage.getItem('pos_invoice');
    if (savedInvoice) {
        const invoiceData = JSON.parse(savedInvoice);
        invoiceItems = invoiceData.items || [];
        vatEnabled = invoiceData.vatEnabled !== undefined ? invoiceData.vatEnabled : true;

        // تحديث حالة مفتاح الضريبة
        if (vatToggle) {
            vatToggle.checked = vatEnabled;
        }
    }
}

function saveSalesHistory() {
    localStorage.setItem('pos_sales_history', JSON.stringify(salesHistory));
}

function loadSalesHistory() {
    const savedSales = localStorage.getItem('pos_sales_history');
    if (savedSales) {
        salesHistory = JSON.parse(savedSales);
    }
}

// وظائف إعدادات الشركة
function saveCompanySettings() {
    companySettings = {
        name: companyNameInput.value.trim(),
        vatNumber: vatNumberInput.value.trim(),
        address: companyAddressInput.value.trim(),
        phone: companyPhoneInput.value.trim()
    };

    localStorage.setItem('pos_company_settings', JSON.stringify(companySettings));
    alert('تم حفظ إعدادات الشركة بنجاح');
}

function loadCompanySettings() {
    const savedSettings = localStorage.getItem('pos_company_settings');
    if (savedSettings) {
        companySettings = JSON.parse(savedSettings);
    }

    // تحديث النموذج
    if (companyNameInput) companyNameInput.value = companySettings.name;
    if (vatNumberInput) vatNumberInput.value = companySettings.vatNumber;
    if (companyAddressInput) companyAddressInput.value = companySettings.address;
    if (companyPhoneInput) companyPhoneInput.value = companySettings.phone;
}
