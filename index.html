<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>نظام المبيعات - المملكة العربية السعودية</title>
    <link rel="stylesheet" href="styles.css">
</head>
<body>
    <div class="container">
        <header>
            <h1>نظام المبيعات</h1>
            <p>المملكة العربية السعودية - ضريبة القيمة المضافة 15%</p>
        </header>

        <!-- شريط التنقل -->
        <nav class="nav-tabs">
            <button class="nav-tab active" data-tab="pos">نقطة البيع</button>
            <button class="nav-tab" data-tab="products">إدارة المنتجات</button>
            <button class="nav-tab" data-tab="sales">سجل المبيعات</button>
            <button class="nav-tab" data-tab="settings">الإعدادات</button>
        </nav>

        <main>
            <!-- واجهة نقطة البيع -->
            <div id="pos-section" class="tab-content active">
                <div class="pos-layout">
                    <section class="products-grid">
                        <h2>المنتجات المتاحة</h2>
                        <div class="vat-control">
                            <label class="switch">
                                <input type="checkbox" id="vatToggle" checked>
                                <span class="slider"></span>
                            </label>
                            <span>تطبيق ضريبة القيمة المضافة (15%)</span>
                        </div>
                        <div id="productsGrid" class="products-container">
                            <p class="empty-message">لا توجد منتجات. اذهب إلى إدارة المنتجات لإضافة منتجات جديدة.</p>
                        </div>
                    </section>

                    <section class="invoice">
                        <div class="invoice-header">
                            <h2>الفاتورة</h2>
                            <div class="invoice-number">رقم الفاتورة: <span id="invoiceNumber">--</span></div>
                        </div>
                        <div class="invoice-items" id="invoiceItems">
                            <p class="empty-message">لا توجد منتجات في الفاتورة</p>
                        </div>

                        <div class="invoice-summary" id="invoiceSummary" style="display: none;">
                            <div class="summary-row">
                                <span>المجموع الفرعي:</span>
                                <span id="subtotal">0.00 ريال</span>
                            </div>
                            <div class="summary-row" id="vatRow">
                                <span>ضريبة القيمة المضافة (15%):</span>
                                <span id="vatAmount">0.00 ريال</span>
                            </div>
                            <div class="summary-row total">
                                <span>المجموع الإجمالي:</span>
                                <span id="total">0.00 ريال</span>
                            </div>

                            <div class="qr-section">
                                <h3>رمز QR للفاتورة</h3>
                                <div id="qrcode" class="qr-container"></div>
                                <p class="qr-info">امسح الرمز للحصول على تفاصيل الفاتورة</p>
                            </div>

                            <div class="invoice-actions">
                                <button id="clearInvoice" class="btn-secondary">مسح الفاتورة</button>
                                <button id="completeInvoice" class="btn-success">إتمام الفاتورة</button>
                                <button id="printInvoice" class="btn-primary">طباعة الفاتورة</button>
                                <button id="printSimple" class="btn-info">طباعة مبسطة</button>
                            </div>
                        </div>
                    </section>
                </div>
            </div>

            <!-- واجهة إدارة المنتجات -->
            <div id="products-section" class="tab-content">
                <section class="product-management">
                    <h2>إدارة المنتجات</h2>

                    <div class="add-product-form">
                        <h3>إضافة منتج جديد</h3>
                        <form id="addProductForm">
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="newProductName">اسم المنتج:</label>
                                    <input type="text" id="newProductName" required>
                                </div>

                                <div class="form-group">
                                    <label for="newProductPrice">السعر:</label>
                                    <input type="number" id="newProductPrice" step="0.01" min="0" required>
                                </div>

                                <div class="form-group">
                                    <label for="newProductCategory">الفئة:</label>
                                    <input type="text" id="newProductCategory" placeholder="اختياري">
                                </div>
                            </div>

                            <button type="submit">إضافة المنتج</button>
                        </form>
                    </div>

                    <div class="products-list">
                        <h3>قائمة المنتجات</h3>
                        <div id="productsList" class="products-table">
                            <p class="empty-message">لا توجد منتجات محفوظة</p>
                        </div>
                    </div>
                </section>
            </div>

            <!-- واجهة سجل المبيعات -->
            <div id="sales-section" class="tab-content">
                <section class="sales-history">
                    <h2>سجل المبيعات</h2>

                    <div class="sales-summary">
                        <div class="summary-card">
                            <h3>إجمالي المبيعات اليوم</h3>
                            <div class="summary-value" id="todaySales">0.00 ريال</div>
                        </div>
                        <div class="summary-card">
                            <h3>عدد الفواتير اليوم</h3>
                            <div class="summary-value" id="todayInvoices">0</div>
                        </div>
                        <div class="summary-card">
                            <h3>متوسط قيمة الفاتورة</h3>
                            <div class="summary-value" id="averageInvoice">0.00 ريال</div>
                        </div>
                    </div>

                    <div class="sales-filters">
                        <button class="filter-btn active" data-filter="today">اليوم</button>
                        <button class="filter-btn" data-filter="week">هذا الأسبوع</button>
                        <button class="filter-btn" data-filter="month">هذا الشهر</button>
                        <button class="filter-btn" data-filter="all">جميع المبيعات</button>
                    </div>

                    <div class="sales-list">
                        <div id="salesList" class="sales-table">
                            <p class="empty-message">لا توجد مبيعات مسجلة</p>
                        </div>
                    </div>
                </section>
            </div>

            <!-- واجهة الإعدادات -->
            <div id="settings-section" class="tab-content">
                <section class="settings">
                    <h2>إعدادات النظام</h2>

                    <div class="settings-group">
                        <h3>معلومات الشركة</h3>
                        <form id="companyForm">
                            <div class="form-row">
                                <div class="form-group">
                                    <label for="companyName">اسم الشركة:</label>
                                    <input type="text" id="companyName" value="شركة المثال التجارية" required>
                                </div>

                                <div class="form-group">
                                    <label for="vatNumber">الرقم الضريبي:</label>
                                    <input type="text" id="vatNumber" value="123456789012345" required maxlength="15">
                                </div>
                            </div>

                            <div class="form-row">
                                <div class="form-group">
                                    <label for="companyAddress">عنوان الشركة:</label>
                                    <textarea id="companyAddress" rows="3" required>الرياض، المملكة العربية السعودية</textarea>
                                </div>

                                <div class="form-group">
                                    <label for="companyPhone">رقم الهاتف:</label>
                                    <input type="tel" id="companyPhone" value="+966501234567">
                                </div>
                            </div>

                            <button type="submit">حفظ الإعدادات</button>
                        </form>
                    </div>

                    <div class="settings-group">
                        <h3>إعدادات الفاتورة</h3>
                        <div class="form-group">
                            <label class="switch">
                                <input type="checkbox" id="includeQRInPrint" checked>
                                <span class="slider"></span>
                            </label>
                            <span>تضمين QR Code في الطباعة</span>
                        </div>

                        <div class="form-group">
                            <label class="switch">
                                <input type="checkbox" id="autoCompleteInvoice">
                                <span class="slider"></span>
                            </label>
                            <span>إتمام الفاتورة تلقائياً عند الطباعة</span>
                        </div>
                    </div>
                </section>
            </div>

        </main>
    </div>

    <!-- مكتبة QR Code -->
    <script src="https://cdn.jsdelivr.net/npm/qrcode@1.5.3/build/qrcode.min.js"></script>
    <script src="script.js"></script>
</body>
</html>
