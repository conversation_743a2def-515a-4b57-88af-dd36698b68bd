* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f5f5f5;
    color: #333;
    line-height: 1.6;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

header {
    background: linear-gradient(135deg, #2c5530, #4a7c59);
    color: white;
    text-align: center;
    padding: 30px;
    border-radius: 10px;
    margin-bottom: 30px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

header h1 {
    font-size: 2.5rem;
    margin-bottom: 10px;
}

header p {
    font-size: 1.1rem;
    opacity: 0.9;
}

main {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 30px;
}

.sales-form, .invoice {
    background: white;
    padding: 30px;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.sales-form h2, .invoice h2 {
    color: #2c5530;
    margin-bottom: 20px;
    font-size: 1.5rem;
    border-bottom: 2px solid #4a7c59;
    padding-bottom: 10px;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: bold;
    color: #555;
}

.form-group input {
    width: 100%;
    padding: 12px;
    border: 2px solid #ddd;
    border-radius: 5px;
    font-size: 1rem;
    transition: border-color 0.3s;
}

.form-group input:focus {
    outline: none;
    border-color: #4a7c59;
}

.currency {
    color: #666;
    font-size: 0.9rem;
    margin-right: 10px;
}

button {
    background: #4a7c59;
    color: white;
    padding: 12px 24px;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    font-size: 1rem;
    transition: background-color 0.3s;
}

button:hover {
    background: #2c5530;
}

.btn-secondary {
    background: #6c757d;
}

.btn-secondary:hover {
    background: #545b62;
}

.btn-primary {
    background: #007bff;
}

.btn-primary:hover {
    background: #0056b3;
}

.invoice-items {
    min-height: 200px;
    margin-bottom: 20px;
}

.empty-message {
    text-align: center;
    color: #999;
    font-style: italic;
    padding: 50px 0;
}

.invoice-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px;
    border: 1px solid #eee;
    border-radius: 5px;
    margin-bottom: 10px;
    background: #f9f9f9;
}

.item-details {
    flex: 1;
}

.item-name {
    font-weight: bold;
    color: #2c5530;
}

.item-info {
    font-size: 0.9rem;
    color: #666;
    margin-top: 5px;
}

.item-total {
    font-weight: bold;
    color: #4a7c59;
    font-size: 1.1rem;
}

.remove-item {
    background: #dc3545;
    color: white;
    border: none;
    padding: 5px 10px;
    border-radius: 3px;
    cursor: pointer;
    margin-right: 10px;
}

.remove-item:hover {
    background: #c82333;
}

.invoice-summary {
    border-top: 2px solid #eee;
    padding-top: 20px;
}

.summary-row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 10px;
    font-size: 1.1rem;
}

.summary-row.total {
    font-weight: bold;
    font-size: 1.3rem;
    color: #2c5530;
    border-top: 2px solid #4a7c59;
    padding-top: 10px;
    margin-top: 15px;
}

.invoice-actions {
    display: flex;
    gap: 10px;
    margin-top: 20px;
}

.invoice-actions button {
    flex: 1;
}

.qr-section {
    text-align: center;
    margin: 20px 0;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 8px;
    border: 2px dashed #4a7c59;
}

.qr-section h3 {
    color: #2c5530;
    margin-bottom: 15px;
    font-size: 1.2rem;
}

.qr-container {
    display: flex;
    justify-content: center;
    align-items: center;
    margin: 15px 0;
    min-height: 150px;
}

.qr-container canvas {
    border: 2px solid #4a7c59;
    border-radius: 8px;
    background: white;
    padding: 10px;
}

.qr-info {
    color: #666;
    font-size: 0.9rem;
    margin-top: 10px;
    font-style: italic;
}

@media (max-width: 768px) {
    main {
        grid-template-columns: 1fr;
    }
    
    header h1 {
        font-size: 2rem;
    }
    
    .container {
        padding: 10px;
    }
    
    .sales-form, .invoice {
        padding: 20px;
    }
}

@print {
    .sales-form, .invoice-actions {
        display: none;
    }

    body {
        background: white;
    }

    .container {
        max-width: none;
        margin: 0;
        padding: 0;
    }

    .qr-section {
        page-break-inside: avoid;
        margin-top: 20px;
    }

    .qr-container canvas {
        border: 1px solid #000;
    }
}
