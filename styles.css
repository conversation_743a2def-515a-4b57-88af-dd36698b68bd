* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f5f5f5;
    color: #333;
    line-height: 1.6;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

header {
    background: linear-gradient(135deg, #2c5530, #4a7c59);
    color: white;
    text-align: center;
    padding: 30px;
    border-radius: 10px;
    margin-bottom: 30px;
    box-shadow: 0 4px 6px rgba(0, 0, 0, 0.1);
}

header h1 {
    font-size: 2.5rem;
    margin-bottom: 10px;
}

header p {
    font-size: 1.1rem;
    opacity: 0.9;
}

/* شريط التنقل */
.nav-tabs {
    display: flex;
    background: white;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
    margin-bottom: 30px;
    overflow: hidden;
}

.nav-tab {
    flex: 1;
    padding: 15px 20px;
    background: #f8f9fa;
    border: none;
    cursor: pointer;
    font-size: 1.1rem;
    font-weight: bold;
    color: #666;
    transition: all 0.3s;
}

.nav-tab.active {
    background: #4a7c59;
    color: white;
}

.nav-tab:hover:not(.active) {
    background: #e9ecef;
}

/* محتوى التبويبات */
.tab-content {
    display: none;
}

.tab-content.active {
    display: block;
}

/* تخطيط نقطة البيع */
.pos-layout {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 30px;
}

.products-grid, .invoice, .product-management {
    background: white;
    padding: 30px;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

/* التحكم في الضريبة */
.vat-control {
    display: flex;
    align-items: center;
    gap: 10px;
    margin-bottom: 20px;
    padding: 15px;
    background: #f8f9fa;
    border-radius: 8px;
    border: 2px solid #e9ecef;
}

/* مفتاح التبديل */
.switch {
    position: relative;
    display: inline-block;
    width: 60px;
    height: 34px;
}

.switch input {
    opacity: 0;
    width: 0;
    height: 0;
}

.slider {
    position: absolute;
    cursor: pointer;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background-color: #ccc;
    transition: .4s;
    border-radius: 34px;
}

.slider:before {
    position: absolute;
    content: "";
    height: 26px;
    width: 26px;
    left: 4px;
    bottom: 4px;
    background-color: white;
    transition: .4s;
    border-radius: 50%;
}

input:checked + .slider {
    background-color: #4a7c59;
}

input:checked + .slider:before {
    transform: translateX(26px);
}

/* شبكة المنتجات */
.products-container {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(200px, 1fr));
    gap: 15px;
    max-height: 400px;
    overflow-y: auto;
    padding: 10px;
    border: 2px solid #e9ecef;
    border-radius: 8px;
}

.product-card {
    background: #f8f9fa;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    padding: 15px;
    text-align: center;
    cursor: pointer;
    transition: all 0.3s;
}

.product-card:hover {
    border-color: #4a7c59;
    background: #e8f5e8;
    transform: translateY(-2px);
}

.product-card.selected {
    border-color: #4a7c59;
    background: #d4edda;
}

.product-name {
    font-weight: bold;
    color: #2c5530;
    margin-bottom: 8px;
}

.product-price {
    color: #666;
    font-size: 1.1rem;
}

.product-category {
    font-size: 0.8rem;
    color: #999;
    margin-top: 5px;
}

.products-grid h2, .invoice h2, .product-management h2, .sales-history h2 {
    color: #2c5530;
    margin-bottom: 20px;
    font-size: 1.5rem;
    border-bottom: 2px solid #4a7c59;
    padding-bottom: 10px;
}

.invoice-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 20px;
}

.invoice-number {
    background: #f8f9fa;
    padding: 8px 15px;
    border-radius: 5px;
    border: 2px solid #4a7c59;
    font-weight: bold;
    color: #2c5530;
}

/* سجل المبيعات */
.sales-history {
    background: white;
    padding: 30px;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0, 0, 0, 0.1);
}

.sales-summary {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 20px;
    margin-bottom: 30px;
}

.summary-card {
    background: linear-gradient(135deg, #4a7c59, #2c5530);
    color: white;
    padding: 20px;
    border-radius: 10px;
    text-align: center;
}

.summary-card h3 {
    margin-bottom: 10px;
    font-size: 1rem;
    opacity: 0.9;
}

.summary-value {
    font-size: 1.8rem;
    font-weight: bold;
}

.sales-filters {
    display: flex;
    gap: 10px;
    margin-bottom: 20px;
    flex-wrap: wrap;
}

.filter-btn {
    background: #f8f9fa;
    border: 2px solid #e9ecef;
    color: #666;
    padding: 10px 20px;
    border-radius: 5px;
    cursor: pointer;
    transition: all 0.3s;
}

.filter-btn.active,
.filter-btn:hover {
    background: #4a7c59;
    border-color: #4a7c59;
    color: white;
}

.sales-table {
    border: 1px solid #e9ecef;
    border-radius: 8px;
    overflow: hidden;
    max-height: 400px;
    overflow-y: auto;
}

.sale-row {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr 1fr 1fr;
    gap: 15px;
    padding: 15px;
    border-bottom: 1px solid #e9ecef;
    align-items: center;
}

.sale-row:last-child {
    border-bottom: none;
}

.sale-row:nth-child(even) {
    background: #f8f9fa;
}

.sale-row.header {
    background: #4a7c59;
    color: white;
    font-weight: bold;
    position: sticky;
    top: 0;
}

/* إدارة المنتجات */
.add-product-form {
    background: #f8f9fa;
    padding: 20px;
    border-radius: 8px;
    margin-bottom: 30px;
}

.add-product-form h3 {
    color: #2c5530;
    margin-bottom: 15px;
}

.form-row {
    display: grid;
    grid-template-columns: 1fr 1fr 1fr;
    gap: 15px;
}

.products-table {
    border: 1px solid #e9ecef;
    border-radius: 8px;
    overflow: hidden;
}

.product-row {
    display: grid;
    grid-template-columns: 2fr 1fr 1fr 1fr;
    gap: 15px;
    padding: 15px;
    border-bottom: 1px solid #e9ecef;
    align-items: center;
}

.product-row:last-child {
    border-bottom: none;
}

.product-row:nth-child(even) {
    background: #f8f9fa;
}

.product-row.header {
    background: #4a7c59;
    color: white;
    font-weight: bold;
}

.product-actions {
    display: flex;
    gap: 5px;
}

.btn-edit {
    background: #ffc107;
    color: #000;
    padding: 5px 10px;
    border: none;
    border-radius: 3px;
    cursor: pointer;
    font-size: 0.8rem;
}

.btn-edit:hover {
    background: #e0a800;
}

.btn-delete {
    background: #dc3545;
    color: white;
    padding: 5px 10px;
    border: none;
    border-radius: 3px;
    cursor: pointer;
    font-size: 0.8rem;
}

.btn-delete:hover {
    background: #c82333;
}

.form-group {
    margin-bottom: 20px;
}

.form-group label {
    display: block;
    margin-bottom: 5px;
    font-weight: bold;
    color: #555;
}

.form-group input {
    width: 100%;
    padding: 12px;
    border: 2px solid #ddd;
    border-radius: 5px;
    font-size: 1rem;
    transition: border-color 0.3s;
}

.form-group input:focus {
    outline: none;
    border-color: #4a7c59;
}

.currency {
    color: #666;
    font-size: 0.9rem;
    margin-right: 10px;
}

button {
    background: #4a7c59;
    color: white;
    padding: 12px 24px;
    border: none;
    border-radius: 5px;
    cursor: pointer;
    font-size: 1rem;
    transition: background-color 0.3s;
}

button:hover {
    background: #2c5530;
}

.btn-secondary {
    background: #6c757d;
}

.btn-secondary:hover {
    background: #545b62;
}

.btn-primary {
    background: #007bff;
}

.btn-primary:hover {
    background: #0056b3;
}

.btn-success {
    background: #28a745;
}

.btn-success:hover {
    background: #218838;
}

.invoice-items {
    min-height: 200px;
    margin-bottom: 20px;
}

.empty-message {
    text-align: center;
    color: #999;
    font-style: italic;
    padding: 50px 0;
}

.invoice-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px;
    border: 1px solid #eee;
    border-radius: 5px;
    margin-bottom: 10px;
    background: #f9f9f9;
}

.item-details {
    flex: 1;
}

.item-name {
    font-weight: bold;
    color: #2c5530;
}

.item-info {
    font-size: 0.9rem;
    color: #666;
    margin-top: 5px;
}

.item-total {
    font-weight: bold;
    color: #4a7c59;
    font-size: 1.1rem;
}

.remove-item {
    background: #dc3545;
    color: white;
    border: none;
    padding: 5px 10px;
    border-radius: 3px;
    cursor: pointer;
    margin-right: 10px;
}

.remove-item:hover {
    background: #c82333;
}

.item-controls {
    display: flex;
    align-items: center;
    gap: 5px;
}

.qty-btn {
    background: #6c757d;
    color: white;
    border: none;
    width: 30px;
    height: 30px;
    border-radius: 50%;
    cursor: pointer;
    font-size: 1rem;
    display: flex;
    align-items: center;
    justify-content: center;
}

.qty-btn:hover {
    background: #545b62;
}

.qty-display {
    background: #f8f9fa;
    border: 1px solid #ddd;
    padding: 5px 10px;
    border-radius: 3px;
    min-width: 40px;
    text-align: center;
    font-weight: bold;
}

.invoice-summary {
    border-top: 2px solid #eee;
    padding-top: 20px;
}

.summary-row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 10px;
    font-size: 1.1rem;
}

.summary-row.total {
    font-weight: bold;
    font-size: 1.3rem;
    color: #2c5530;
    border-top: 2px solid #4a7c59;
    padding-top: 10px;
    margin-top: 15px;
}

.invoice-actions {
    display: flex;
    gap: 10px;
    margin-top: 20px;
}

.invoice-actions button {
    flex: 1;
}

.qr-section {
    text-align: center;
    margin: 20px 0;
    padding: 20px;
    background: #f8f9fa;
    border-radius: 8px;
    border: 2px dashed #4a7c59;
}

.qr-section h3 {
    color: #2c5530;
    margin-bottom: 15px;
    font-size: 1.2rem;
}

.qr-container {
    display: flex;
    justify-content: center;
    align-items: center;
    margin: 15px 0;
    min-height: 150px;
}

.qr-container canvas {
    border: 2px solid #4a7c59;
    border-radius: 8px;
    background: white;
    padding: 10px;
}

.qr-info {
    color: #666;
    font-size: 0.9rem;
    margin-top: 10px;
    font-style: italic;
}

@media (max-width: 768px) {
    .pos-layout {
        grid-template-columns: 1fr;
    }

    .nav-tabs {
        flex-direction: column;
    }

    .form-row {
        grid-template-columns: 1fr;
    }

    .product-row {
        grid-template-columns: 1fr;
        text-align: center;
    }

    .products-container {
        grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    }

    header h1 {
        font-size: 2rem;
    }

    .container {
        padding: 10px;
    }

    .products-grid, .invoice, .product-management {
        padding: 20px;
    }
}

@print {
    .sales-form, .invoice-actions {
        display: none;
    }

    body {
        background: white;
    }

    .container {
        max-width: none;
        margin: 0;
        padding: 0;
    }

    .qr-section {
        page-break-inside: avoid;
        margin-top: 20px;
    }

    .qr-container canvas {
        border: 1px solid #000;
    }
}
